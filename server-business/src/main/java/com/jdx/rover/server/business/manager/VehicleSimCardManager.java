/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.server.business.manager;

import cn.hutool.core.collection.CollUtil;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * SIM卡异常告警管理器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 * @description SIM卡异常告警处理
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleSimCardManager {

    /**
     * VehicleAlarmManager
     */
    private final VehicleAlarmManager vehicleAlarmManager;

    /**
     * RedissonRepository
     */
    private final RedissonRepository redissonRepository;

    /**
     * ReportAlarmRepository
     */
    private final ReportAlarmRepository reportAlarmRepository;

    /**
     * 4G状态持续时间阈值（秒）
     */
    private static final int G4_DURATION_THRESHOLD = 10;

    /**
     * 5G信号质量持续时间阈值（秒）
     */
    private static final int G5_SIGNAL_DURATION_THRESHOLD = 5;

    /**
     * RSRP阈值
     */
    private static final float RSRP_THRESHOLD = -100.0f;

    /**
     * SINR阈值
     */
    private static final float SINR_THRESHOLD = 3.0f;

    /**
     * Redis ZSet key前缀 - 4G状态持续时间
     */
    private static final String REDIS_KEY_4G_DURATION = "sim_card_4g_duration:";

    /**
     * Redis ZSet key前缀 - 5G信号质量持续时间
     */
    private static final String REDIS_KEY_5G_SIGNAL_DURATION = "sim_card_5g_signal_duration:";

    /**
     * ZSet过期时间（秒）
     */
    private static final long ZSET_EXPIRE_TIME = 3600;

    /**
     * 处理SIM卡异常告警
     *
     * @param vehicleName 车辆名称
     * @param simStatusList SIM卡状态列表
     * @param simDataList SIM卡数据列表
     * @param recordTime 记录时间
     */
    public void handleSimCardAbnormal(String vehicleName, List<SimStatusDTO> simStatusList, List<SimDataDTO> simDataList, Date recordTime) {
        if (CollUtil.isEmpty(simStatusList) && CollUtil.isEmpty(simDataList)) {
            return;
        }

        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        VehicleAlarmEventDTO alarmDb = alarmMapDb.get(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        boolean shouldGenerateAlarm = false;
        StringBuilder errorMessage = new StringBuilder();

        // 检查条件1：车身域任意sim卡online_status上报离线后上报
        if (CollUtil.isNotEmpty(simStatusList)) {
            for (SimStatusDTO simStatus : simStatusList) {
                if (simStatus.getOnlineStatus() != null && simStatus.getOnlineStatus() == 1) { // 1-离线
                    shouldGenerateAlarm = true;
                    errorMessage.append("SIM卡设备ID").append(simStatus.getDeviceId()).append("离线；");
                    log.info("【SIM卡异常】{} SIM卡设备ID{}离线", vehicleName, simStatus.getDeviceId());
                    break;
                }
            }
        }

        // 检查条件2：任意sim卡为4G状态持续10秒
        if (!shouldGenerateAlarm && CollUtil.isNotEmpty(simDataList)) {
            shouldGenerateAlarm = check4GDuration(vehicleName, simDataList, recordTime, errorMessage);
        }

        // 检查条件3：当SIM卡5G模式时，但RSRP<-100，或者SINR<3时，持续5秒
        if (!shouldGenerateAlarm && CollUtil.isNotEmpty(simDataList)) {
            shouldGenerateAlarm = check5GSignalQuality(vehicleName, simDataList, recordTime, errorMessage);
        }

        // 生成告警
        if (shouldGenerateAlarm && Objects.isNull(alarmDb)) {
            VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
            alarm.setType(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
            alarm.setReportTime(recordTime);
            alarm.setErrorCode(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
            alarm.setErrorMessage(errorMessage.toString());
            vehicleAlarmManager.addAlarm(vehicleName, alarm);
            log.info("【SIM卡异常】生成SIM卡异常告警，车辆：{}，原因：{}", vehicleName, errorMessage);
            return;
        }

        // 检查告警消除条件
        if (!Objects.isNull(alarmDb)) {
            boolean shouldRemoveAlarm = checkAlarmRemovalConditions(vehicleName, simStatusList, simDataList);
            if (shouldRemoveAlarm) {
                vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
                log.info("【SIM卡异常】消除SIM卡异常告警，车辆：{}", vehicleName);
            }
        }
    }

    /**
     * 检查4G状态持续时间
     */
    private boolean check4GDuration(String vehicleName, List<SimDataDTO> simDataList, Date recordTime, StringBuilder errorMessage) {
        long currentTime = recordTime.getTime();
        String redisKey = REDIS_KEY_4G_DURATION + vehicleName;

        for (SimDataDTO simData : simDataList) {
            String deviceKey = getDeviceKey(simData, currentTime);
            if (simData.getStandard() != null && simData.getStandard().equals("LTE")) {
                // 添加当前时间点到ZSet
                redissonRepository.addToScoredSortedSet(redisKey, deviceKey, currentTime);
                redissonRepository.expireScoredSortedSet(redisKey, ZSET_EXPIRE_TIME);

                // 清理过期数据（超过阈值时间的数据）
                long thresholdTime = currentTime - G4_DURATION_THRESHOLD * 1000L;
                redissonRepository.removeRangeByScore(redisKey, 0, thresholdTime);

                // 检查是否满足持续时间条件
                int count = redissonRepository.countByScore(redisKey, thresholdTime, currentTime);
                if (count >= G4_DURATION_THRESHOLD) {
                    errorMessage.append("SIM卡设备ID").append(simData.getDeviceId()).append("4G状态持续").append(G4_DURATION_THRESHOLD).append("秒；");
                    log.info("【SIM卡异常】{}SIM卡设备ID{}4G状态持续{}秒", vehicleName, simData.getDeviceId(), G4_DURATION_THRESHOLD);
                    return true;
                }
            } else {
                // 如果不是4G状态，清理该设备的记录
                redissonRepository.removeFromScoredSortedSet(redisKey, deviceKey);
            }
        }
        return false;
    }

    /**
     * 检查5G信号质量持续时间
     */
    private boolean check5GSignalQuality(String vehicleName, List<SimDataDTO> simDataList, Date recordTime, StringBuilder errorMessage) {
        long currentTime = recordTime.getTime();
        String redisKey = REDIS_KEY_5G_SIGNAL_DURATION + vehicleName;

        for (SimDataDTO simData : simDataList) {
            if (simData.getStandard() != null && simData.getStandard().equals("5G NR")) {
                boolean poorSignal = (simData.getRsrp() != null && simData.getRsrp() < RSRP_THRESHOLD) || (simData.getSinr() != null && simData.getSinr() < SINR_THRESHOLD);

                String deviceKey = getDeviceKey(simData, currentTime);
                if (poorSignal) {
                    // 添加当前时间点到ZSet
                    redissonRepository.addToScoredSortedSet(redisKey, deviceKey, currentTime);
                    redissonRepository.expireScoredSortedSet(redisKey, ZSET_EXPIRE_TIME);

                    // 清理过期数据
                    long thresholdTime = currentTime - G5_SIGNAL_DURATION_THRESHOLD * 1000L;
                    redissonRepository.removeRangeByScore(redisKey, 0, thresholdTime);

                    // 检查是否满足持续时间条件
                    int count = redissonRepository.countByScore(redisKey, thresholdTime, currentTime);
                    if (count >= G5_SIGNAL_DURATION_THRESHOLD) {
                        errorMessage.append("SIM卡设备ID").append(simData.getDeviceId())
                                   .append("5G信号质量差(RSRP:").append(simData.getRsrp())
                                   .append(",SINR:").append(simData.getSinr()).append(")持续")
                                   .append(G5_SIGNAL_DURATION_THRESHOLD).append("秒；");
                        log.info("【SIM卡异常】{}SIM卡设备ID{}5G信号质量差(RSRP:{},SINR:{})持续{}秒", vehicleName, simData.getDeviceId(), simData.getRsrp(), simData.getSinr(), G5_SIGNAL_DURATION_THRESHOLD);
                        return true;
                    }
                } else {
                    // 如果信号质量正常，清理该设备的记录
                    redissonRepository.removeFromScoredSortedSet(redisKey, deviceKey);
                }
            }
        }
        return false;
    }

    /**
     * 检查告警消除条件
     */
    private boolean checkAlarmRemovalConditions(String vehicleName, List<SimStatusDTO> simStatusList, List<SimDataDTO> simDataList) {
        // 条件1：车身域online_status不再上报离线
        if (CollUtil.isNotEmpty(simStatusList)) {
            for (SimStatusDTO simStatus : simStatusList) {
                if (simStatus.getOnlineStatus() != null && simStatus.getOnlineStatus() == 1) { // 1-离线
                    return false;
                }
            }
        }

        // 条件2：sim卡均不为4G状态时
        boolean has4G = false;
        if (CollUtil.isNotEmpty(simDataList)) {
            for (SimDataDTO simData : simDataList) {
                if (simData.getStandard() != null && simData.getStandard().equals("LTE")) {
                    has4G = true;
                    break;
                }
            }
        }

        // 条件3：当SIM卡5G模式时，但RSRP大于等于-100，且SINR大于3时
        boolean has5GWithPoorSignal = false;
        if (CollUtil.isNotEmpty(simDataList)) {
            for (SimDataDTO simData : simDataList) {
                if (simData.getStandard() != null && simData.getStandard().equals("5G NR")) {
                    boolean poorSignal = (simData.getRsrp() != null && simData.getRsrp() < RSRP_THRESHOLD) || (simData.getSinr() != null && simData.getSinr() < SINR_THRESHOLD);
                    if (poorSignal) {
                        has5GWithPoorSignal = true;
                        break;
                    }
                }
            }
        }

        // 如果没有4G状态且没有5G信号质量差的情况，则可以消除告警
        if (!has4G && !has5GWithPoorSignal) {
            // 清理Redis中的持续时间记录
            String redisKey4G = REDIS_KEY_4G_DURATION + vehicleName;
            String redisKey5G = REDIS_KEY_5G_SIGNAL_DURATION + vehicleName;
            redissonRepository.deleteObject(redisKey4G);
            redissonRepository.deleteObject(redisKey5G);
            return true;
        }

        return false;
    }

    /**
     * 获取设备键
     *
     * @param simData SIM卡数据
     * @param recordTime 记录时间
     * @return 设备键
     */
    private static String getDeviceKey(SimDataDTO simData, long recordTime) {
        return "device_" + simData.getDeviceId() + "_" + recordTime;
    }
}
